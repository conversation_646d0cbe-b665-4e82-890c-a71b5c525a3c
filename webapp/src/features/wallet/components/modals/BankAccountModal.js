import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import {
  faTimes,
  faUniversity,
  faPlus,
  faEdit,
  faShieldAlt,
  faCheckCircle,
  faExclamationTriangle
} from '@fortawesome/free-solid-svg-icons';
import BankAccountFormEnhanced from '../../../banking/components/forms/BankAccountFormEnhanced';

/**
 * Bank Account Modal Component
 *
 * Full-screen modal for adding and editing bank accounts with glass-morphism design
 * and smooth animations, following the wallet UI patterns.
 */
const BankAccountModal = ({
  isOpen = false,
  mode = 'add', // 'add' or 'edit'
  account = null, // For editing existing account
  onClose,
  onSuccess,
  className = ''
}) => {
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Reset state when modal closes
  useEffect(() => {
    if (!isOpen) {
      setIsSubmitting(false);
    }
  }, [isOpen]);

  // Handle form submission
  const handleFormSuccess = async (accountData) => {
    try {
      setIsSubmitting(true);
      await onSuccess(accountData);
      // Modal will be closed by parent component
    } catch (error) {
      console.error('Bank account operation failed:', error);
      // Error is handled by parent component and context
    } finally {
      setIsSubmitting(false);
    }
  };

  // Handle form cancellation
  const handleFormCancel = () => {
    if (!isSubmitting) {
      onClose();
    }
  };

  // Handle modal close
  const handleClose = () => {
    if (!isSubmitting) {
      onClose();
    }
  };

  if (!isOpen) return null;

  return (
    <AnimatePresence>
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
        className="fixed inset-0 z-50 flex items-center justify-center p-4 bg-black/50 backdrop-blur-sm"
        onClick={handleClose}
      >
        <motion.div
          initial={{ scale: 0.9, opacity: 0 }}
          animate={{ scale: 1, opacity: 1 }}
          exit={{ scale: 0.9, opacity: 0 }}
          onClick={(e) => e.stopPropagation()}
          className={`
            w-full max-w-2xl max-h-[90vh] overflow-y-auto
            bg-white/95 backdrop-blur-xl rounded-3xl shadow-2xl
            border border-white/30 ${className}
          `}
        >
          {/* Header */}
          <div className="sticky top-0 bg-white/90 backdrop-blur-sm border-b border-gray-100 p-6 rounded-t-3xl">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <div className="w-12 h-12 rounded-xl bg-gradient-to-br from-green-500 to-emerald-600 flex items-center justify-center shadow-lg">
                  <FontAwesomeIcon
                    icon={mode === 'edit' ? faEdit : faPlus}
                    className="text-white text-xl"
                  />
                </div>
                <div>
                  <h2 className="text-2xl font-bold text-gray-900">
                    {mode === 'edit' ? 'Edit Bank Account' : 'Add Bank Account'}
                  </h2>
                  <p className="text-gray-600">
                    {mode === 'edit'
                      ? 'Update your bank account information'
                      : 'Add a new bank account for withdrawals'
                    }
                  </p>
                </div>
              </div>

              {!isSubmitting && (
                <motion.button
                  whileHover={{ scale: 1.1 }}
                  whileTap={{ scale: 0.9 }}
                  onClick={handleClose}
                  className="w-10 h-10 rounded-full bg-gray-100 hover:bg-gray-200 flex items-center justify-center transition-colors"
                >
                  <FontAwesomeIcon icon={faTimes} className="text-gray-600" />
                </motion.button>
              )}
            </div>
          </div>

          {/* Content */}
          <div className="p-6">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              className="space-y-6"
            >
              {/* Security Notice */}
              <div className="bg-green-50 border border-green-200 rounded-xl p-4">
                <div className="flex items-center space-x-3">
                  <FontAwesomeIcon icon={faShieldAlt} className="text-green-600" />
                  <div>
                    <p className="font-medium text-green-900">Secure & Encrypted</p>
                    <p className="text-sm text-green-700">
                      Your bank account information is encrypted and securely stored
                    </p>
                  </div>
                </div>
              </div>

              {/* Bank Account Form */}
              <div className="bg-gradient-to-br from-gray-50/50 to-white/50 rounded-2xl p-6 border border-gray-100">
                <BankAccountFormEnhanced
                  mode={mode}
                  account={account}
                  onSuccess={handleFormSuccess}
                  onCancel={handleFormCancel}
                  size="large"
                />
              </div>

              {/* Additional Information */}
              <div className="bg-blue-50 border border-blue-200 rounded-xl p-4">
                <div className="flex items-start space-x-3">
                  <FontAwesomeIcon icon={faUniversity} className="text-blue-600 mt-1" />
                  <div>
                    <p className="font-medium text-blue-900 mb-2">Important Information</p>
                    <ul className="text-sm text-blue-700 space-y-1">
                      <li>• Only Malaysian bank accounts are supported</li>
                      <li>• Account holder name must match your registered name</li>
                      <li>• Verification may take 1-2 business days</li>
                      <li>• You can set one account as primary for faster withdrawals</li>
                    </ul>
                  </div>
                </div>
              </div>
            </motion.div>
          </div>
        </motion.div>
      </motion.div>
    </AnimatePresence>
  );
};

export default BankAccountModal;
