import React, { useEffect } from 'react';
import { motion } from 'framer-motion';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import {
  faUniversity,
  faPlus,
  faEdit,
  faTrash,
  faStar,
  faSpinner,
  faExclamationTriangle,
  faCheckCircle,
  faCrown
} from '@fortawesome/free-solid-svg-icons';
import { useBankAccountsEnhanced } from '../../../banking/hooks/useBankAccountsEnhanced';
import { formatBankAccountForDisplay, getBankAccountStatus } from '../../../banking/utils/bankingUtils';
import WalletErrorHandler from '../common/WalletErrorHandler';

/**
 * BankAccountManager Component
 *
 * Modern bank account management interface with glass-morphism effects,
 * following the wallet design patterns for consistent user experience.
 */
const BankAccountManager = ({
  showAddButton = true,
  showTitle = true,
  onAccountSelect,
  selectedAccountId = null,
  variant = 'card', // 'card', 'list', 'minimal'
  size = 'medium',
  className = '',
  showErrorHandler = true,
  showVerificationStatus = true,
  showPrimaryIndicator = true,
  onAddAccount, // Callback to open add modal
  onEditAccount // Callback to open edit modal
}) => {
  const {
    accounts,
    loading,
    errors,
    loadBankAccounts,
    deleteBankAccount,
    setPrimaryAccount,
    refresh: refreshBankAccounts,
    isOperationLoading,
    hasAccounts,
    primaryAccount
  } = useBankAccountsEnhanced();



  // Load bank accounts on mount
  useEffect(() => {
    if (!accounts.length && !loading) {
      loadBankAccounts();
    }
  }, [accounts.length, loading, loadBankAccounts]);

  // Format accounts for display
  const formattedAccounts = accounts.map(account => formatBankAccountForDisplay(account));



  const handleDeleteAccount = async (accountId) => {
    if (!window.confirm('Are you sure you want to delete this bank account?')) {
      return;
    }

    try {
      await deleteBankAccount(accountId);
    } catch (err) {
      console.error('Error deleting bank account:', err);
      // Error is handled by context and WalletErrorHandler
    }
  };

  const handleSetPrimary = async (accountId) => {
    try {
      await setPrimaryAccount(accountId);
    } catch (err) {
      console.error('Error setting primary account:', err);
      // Error is handled by context and WalletErrorHandler
    }
  };



  const getSizeClasses = () => {
    const sizes = {
      small: {
        container: 'p-3',
        title: 'text-lg',
        card: 'p-3',
        text: 'text-sm'
      },
      medium: {
        container: 'p-4',
        title: 'text-xl',
        card: 'p-4',
        text: 'text-base'
      },
      large: {
        container: 'p-6',
        title: 'text-2xl',
        card: 'p-5',
        text: 'text-lg'
      }
    };
    return sizes[size] || sizes.medium;
  };

  const sizeClasses = getSizeClasses();

  if (variant === 'minimal') {
    return (
      <div className={`bank-account-minimal ${className}`}>
        {loading ? (
          <div className="flex items-center justify-center py-4">
            <FontAwesomeIcon icon={faSpinner} className="animate-spin text-blue-500 mr-2" />
            <span className="text-gray-600">Loading accounts...</span>
          </div>
        ) : accounts.length === 0 ? (
          <div className="text-center py-4 text-gray-500">
            No bank accounts added
          </div>
        ) : (
          <div className="space-y-2">
            {formattedAccounts.slice(0, 2).map((account) => {
              const status = getBankAccountStatus(account);
              return (
                <div
                  key={account.id}
                  onClick={() => onAccountSelect && onAccountSelect(account)}
                  className={`
                    flex items-center justify-between p-3 rounded-lg border transition-all cursor-pointer
                    ${selectedAccountId === account.id
                      ? 'border-blue-500 bg-blue-50'
                      : 'border-gray-200 hover:border-blue-200 hover:bg-blue-25'
                    }
                  `}
                >
                  <div className="flex items-center">
                    <div className="w-8 h-8 rounded-full bg-gradient-to-br from-blue-500 to-indigo-600 flex items-center justify-center mr-3">
                      <FontAwesomeIcon icon={faUniversity} className="text-white text-xs" />
                    </div>
                    <div>
                      <p className="font-medium text-gray-900 text-sm">
                        {account.bankName}
                      </p>
                      <p className="text-xs text-gray-500">
                        {account.maskedAccountNumber}
                      </p>
                    </div>
                  </div>
                  <div className="flex items-center space-x-1">
                    {showVerificationStatus && (
                      <div className={`w-2 h-2 rounded-full ${status.color === 'green' ? 'bg-green-500' : status.color === 'blue' ? 'bg-blue-500' : 'bg-gray-400'}`} />
                    )}
                    {showPrimaryIndicator && account.is_primary && (
                      <FontAwesomeIcon icon={faCrown} className="text-yellow-500 text-sm" />
                    )}
                  </div>
                </div>
              );
            })}
          </div>
        )}
      </div>
    );
  }

  return (
    <motion.div
      className={`
        relative bg-gradient-to-br from-white/90 to-white/70 backdrop-blur-2xl
        border border-white/30 shadow-2xl rounded-2xl overflow-hidden
        ${sizeClasses.container} ${className}
      `}
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.6 }}
      whileHover={{ y: -5, boxShadow: '0 25px 50px -12px rgba(0,0,0,0.15)' }}
    >
      {/* Animated background decorations */}
      <div className="absolute -top-10 -right-10 w-32 h-32 rounded-full bg-gradient-to-br from-green-500/20 to-emerald-500/20 blur-2xl animate-pulse" />
      <div className="absolute -bottom-10 -left-10 w-24 h-24 rounded-full bg-gradient-to-br from-blue-500/20 to-green-500/20 blur-xl animate-pulse delay-1000" />

      {/* Content */}
      <div className="relative z-10">
        {/* Error Handler */}
        {showErrorHandler && (
          <WalletErrorHandler
            context="bank_accounts"
            onRetry={refreshBankAccounts}
            showToasts={false}
            showInlineErrors={true}
            autoRecovery={true}
          />
        )}

        {/* Header */}
        {showTitle && (
          <div className="flex items-center justify-between mb-6">
            <div className="flex items-center">
              <div className="w-12 h-12 rounded-xl bg-gradient-to-br from-green-500 to-emerald-600 flex items-center justify-center mr-4">
                <FontAwesomeIcon icon={faUniversity} className="text-white text-lg" />
              </div>
              <div>
                <h3 className={`font-bold text-gray-900 ${sizeClasses.title}`}>
                  Bank Accounts
                </h3>
                <p className="text-gray-600 text-sm">
                  {accounts.length} account{accounts.length !== 1 ? 's' : ''} configured
                </p>
              </div>
            </div>

            {showAddButton && onAddAccount && (
              <motion.button
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                onClick={onAddAccount}
                className="px-4 py-2 bg-gradient-to-r from-green-500 to-emerald-600 text-white rounded-lg hover:from-green-600 hover:to-emerald-700 transition-all font-medium"
              >
                <FontAwesomeIcon icon={faPlus} className="mr-2" />
                Add Account
              </motion.button>
            )}
          </div>
        )}

        {/* Error State */}
        {errors.accounts && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            className="mb-6 p-4 bg-red-50 border border-red-200 rounded-xl"
          >
            <div className="flex items-center">
              <FontAwesomeIcon icon={faExclamationTriangle} className="text-red-600 mr-3" />
              <div>
                <p className="text-red-800 font-medium">Error</p>
                <p className="text-red-600 text-sm">{errors.accounts}</p>
              </div>
            </div>
          </motion.div>
        )}

        {/* Loading State */}
        {loading.accounts && (
          <div className="space-y-4">
            {[...Array(2)].map((_, i) => (
              <div key={i} className="animate-pulse">
                <div className="flex items-center space-x-4">
                  <div className="w-12 h-12 bg-gray-200 rounded-lg"></div>
                  <div className="flex-1">
                    <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                    <div className="h-3 bg-gray-200 rounded w-1/2"></div>
                  </div>
                  <div className="h-8 bg-gray-200 rounded w-20"></div>
                </div>
              </div>
            ))}
          </div>
        )}

        {/* Bank Accounts List */}
        {!loading && accounts.length === 0 ? (
          <div className="text-center py-12">
            <div className="w-16 h-16 mx-auto mb-4 rounded-full bg-gray-100 flex items-center justify-center">
              <FontAwesomeIcon icon={faUniversity} className="text-gray-400 text-xl" />
            </div>
            <h4 className="text-lg font-medium text-gray-900 mb-2">No bank accounts yet</h4>
            <p className="text-gray-600 mb-4">Add a bank account to enable withdrawals</p>
            {showAddButton && onAddAccount && (
              <motion.button
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
                onClick={onAddAccount}
                className="px-6 py-3 bg-gradient-to-r from-green-500 to-emerald-600 text-white rounded-xl font-medium hover:from-green-600 hover:to-emerald-700 transition-all"
              >
                <FontAwesomeIcon icon={faPlus} className="mr-2" />
                Add Your First Account
              </motion.button>
            )}
          </div>
        ) : (
          <div className="space-y-4">
            {formattedAccounts.map((account, index) => {
              const status = getBankAccountStatus(account);
              return (
                <motion.div
                  key={account.id}
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ delay: index * 0.1 }}
                  onClick={() => onAccountSelect && onAccountSelect(account)}
                  className={`
                    relative p-4 rounded-xl border transition-all
                    ${onAccountSelect ? 'cursor-pointer hover:shadow-md' : ''}
                    ${selectedAccountId === account.id
                      ? 'border-green-500 bg-green-50'
                      : 'border-gray-200 hover:border-green-200'
                    }
                    ${account.is_primary ? 'ring-2 ring-yellow-200' : ''}
                  `}
                >
                  {/* Primary Badge */}
                  {showPrimaryIndicator && account.is_primary && (
                    <div className="absolute -top-2 -right-2 bg-gradient-to-r from-yellow-500 to-orange-500 text-white text-xs rounded-full px-2 py-1 font-bold shadow-lg">
                      <FontAwesomeIcon icon={faCrown} className="mr-1" />
                      Primary
                    </div>
                  )}

                  <div className="flex items-center justify-between">
                    <div className="flex items-center flex-1">
                      <div className="w-12 h-12 rounded-lg bg-gradient-to-br from-blue-500 to-indigo-600 flex items-center justify-center mr-4">
                        <FontAwesomeIcon icon={faUniversity} className="text-white text-lg" />
                      </div>

                      <div className="flex-1 min-w-0">
                        <div className="flex items-center space-x-2 mb-1">
                          <h4 className="font-semibold text-gray-900">
                            {account.bankName}
                          </h4>
                          {showVerificationStatus && (
                            <div className={`flex items-center space-x-1 px-2 py-1 rounded-full text-xs ${
                              status.status === 'verified'
                                ? 'bg-green-100 text-green-800'
                                : status.status === 'pending'
                                ? 'bg-blue-100 text-blue-800'
                                : 'bg-gray-100 text-gray-800'
                            }`}>
                              <FontAwesomeIcon
                                icon={status.status === 'verified' ? faCheckCircle : faExclamationTriangle}
                                className="text-xs"
                              />
                              <span>{status.message}</span>
                            </div>
                          )}
                        </div>
                        <p className="text-gray-600 text-sm">
                          {account.account_holder_name}
                        </p>
                        <p className="text-gray-500 text-sm">
                          {account.maskedAccountNumber}
                        </p>
                      </div>
                    </div>

                  <div className="flex items-center space-x-2">
                    {!account.is_primary && (
                      <motion.button
                        whileHover={{ scale: 1.05 }}
                        whileTap={{ scale: 0.95 }}
                        onClick={(e) => {
                          e.stopPropagation();
                          handleSetPrimary(account.id);
                        }}
                        className="p-2 text-gray-400 hover:text-yellow-500 transition-colors"
                        title="Set as primary"
                      >
                        <FontAwesomeIcon icon={faStar} />
                      </motion.button>
                    )}

                    {onEditAccount && (
                      <motion.button
                        whileHover={{ scale: 1.05 }}
                        whileTap={{ scale: 0.95 }}
                        onClick={(e) => {
                          e.stopPropagation();
                          onEditAccount(account);
                        }}
                        className="p-2 text-gray-400 hover:text-blue-500 transition-colors"
                        title="Edit account"
                      >
                        <FontAwesomeIcon icon={faEdit} />
                      </motion.button>
                    )}

                    <motion.button
                      whileHover={{ scale: 1.05 }}
                      whileTap={{ scale: 0.95 }}
                      onClick={(e) => {
                        e.stopPropagation();
                        handleDeleteAccount(account.id);
                      }}
                      className="p-2 text-gray-400 hover:text-red-500 transition-colors"
                      title="Delete account"
                    >
                      <FontAwesomeIcon icon={faTrash} />
                    </motion.button>
                  </div>
                </div>
              </motion.div>
              );
            })}
          </div>
        )}
      </div>


    </motion.div>
  );
};

export default BankAccountManager;
